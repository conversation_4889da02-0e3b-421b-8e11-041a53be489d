{% extends "base.html" %}
{% load i18n %}
{% load account socialaccount %}
{% load crispy_forms_tags %}

{# ============================= SignUp Template
----------------------------- Purpose: User registration form Dependencies: -
base.html template - i18n translations - crispy forms
============================= #}

{% block head_title %}{% trans "Sign Up" %}{% endblock %}

{% block content %}
{# Container - Main signup form wrapper #}
<div class="container col-md-8 col-sm-12">
  {# Header Section #}
  <div class="masthead">
    <div class="container card-body">
      <div class="row g-0">
        <div class="col-md-12 masthead-text">
          <h1 class="post-title">{% trans "Join Us" %}</h1>
        </div>
      </div>
    </div>
  </div>

  {# Content Section #}
  <div class="row">
    <div class="col card mb-4">
      <div class="card-body">
        {# Welcome Message #}
        <div class="text-center px-4">
          <p>{% trans "Sign Up!" %}</p>

          <p>
            Woohoo! Ready to hop aboard the NederLearn express? We're so excited
            to have you join our awesome language-learning family! Let's get
            this party started!
          </p>

          {# Separator #}
          <div class="row">
            <div class="col">
              <hr>
            </div>
          </div>

          {# Navigation Links #}
          <p>
            <small>
              {% trans "Not convinced yet? Read" %}
              <a class="link" href="{% url 'about_us' %}"
                >{% trans "About Us" %}</a
              >
              {% trans "first." %}
            </small>
            <br>
            <small>
              {% trans "Already a member?" %}
              <a class="link" href="{% url 'account_login' %}"
                >{% trans "Log In" %}</a
              >
              {% trans "." %}
            </small>
          </p>
        </div>

        {# Separator #}
        <div class="row">
          <div class="col">
            <hr>
          </div>
        </div>

        {# Form Section #}
        <form
          class="signup mt-4"
          id="signup_form"
          method="post"
          action="{% url 'account_signup' %}"
        >
          {% csrf_token %}
          {# Crispy Forms Integration #}
          {{ form|crispy }}

          {# Redirect Field #}
          {% if redirect_field_value %}
          <input
            type="hidden"
            name="{{ redirect_field_name }}"
            value="{{ redirect_field_value }}"
          >
          {% endif %}

          {# Submit Button #}
          <div class="text-center mt-4">
            <button class="btn btn-primary btn-lg" type="submit">
              {% trans "Sign Up" %}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

{% endblock %}
